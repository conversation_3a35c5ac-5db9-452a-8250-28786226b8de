# 生产环境数据生成脚本

这个脚本用于向生产环境服务器生成各种类型的数据，主要包括字典值数据和其他业务数据。

## 功能特性

- ✅ **字典值生成**: 支持多种类型的字典数据生成（疾病、治疗方式、科室等）
- ✅ **数据验证**: 内置数据验证机制，确保数据质量
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **日志记录**: 详细的日志记录，支持文件保存
- ✅ **环境配置**: 支持多环境配置（生产、测试、开发）
- ✅ **批量处理**: 支持批量数据生成和处理
- ✅ **可扩展性**: 易于扩展新的数据类型和接口

## 快速开始

### 1. 基本用法

```bash
# 生成默认疾病字典数据
pnpm run gen:data

# 显示帮助信息
pnpm run gen:data:help
```

### 2. 字典数据生成

```bash
# 生成所有类型的字典数据
pnpm run gen:data:all

# 生成疾病类型字典
pnpm run gen:data:diseases
```

### 3. 高级用法

```bash
# 在测试环境运行
node scripts/generate-production-data.js --env=staging

# 生成指定类型的字典数据
node scripts/generate-production-data.js --type=diseases

# 生成所有字典类型
node scripts/generate-production-data.js --all-dictionaries
```

## 配置说明

### 环境配置

脚本支持三种环境配置：

- **production**: 生产环境（默认）
- **staging**: 测试环境
- **development**: 开发环境

配置文件位于 `scripts/data-generation-config.js`

### 字典类型配置

当前支持的字典类型：

| 类型 | 描述 | 字典ID | 编码前缀 |
|------|------|--------|----------|
| diseases | 疾病类型 | 7 | DISEASE |

### 数据模板

字典值数据模板包含以下字段：

```javascript
{
  title: '显示名称',
  value: '实际值',
  type: '字典类型',
  sortOrder: 排序号
}
```

## 生成的数据

### 疾病类型字典

脚本会生成以下疾病类型数据：

1. 药物成瘾
2. 心理健康
3. 焦虑障碍
4. 睡眠障碍
5. 青少年心理健康状况调查数据

### 编码规则

字典编码按以下规则生成：

```
格式: {前缀}{日期}{时间}
示例: DISEASE20250104143022
```

- 前缀：根据字典类型确定（DISEASE、TREATMENT、DEPT等）
- 日期：YYYYMMDD格式
- 时间：HHMMSS格式

## 日志和监控

### 日志级别

- **debug**: 详细调试信息
- **info**: 一般信息
- **warn**: 警告信息
- **error**: 错误信息

### 日志文件

日志文件保存在 `./logs` 目录下，按日期命名：

```
logs/data-generation-2025-01-04.log
```

### 执行结果

脚本执行完成后会显示详细的结果统计：

```
📊 执行结果汇总:
==================================================

📚 diseases 字典值生成结果:
1. ✅ 药物成瘾 (DISEASE20250104143022)
2. ✅ 心理健康 (DISEASE20250104143023)
...

📈 总体成功率: 9/9 (100.0%)
⏰ 结束时间: 2025-01-04 14:30:45
```

## 错误处理

### HTTP错误

脚本会自动处理常见的HTTP错误：

- **400**: 请求参数错误
- **401**: 认证失败
- **403**: 权限不足
- **404**: 接口不存在
- **500**: 服务器内部错误

### 重试机制

- 默认重试次数：3次
- 重试间隔：1000ms
- 请求超时：30秒

### 数据验证

脚本会验证以下数据：

- 标题长度：1-100字符
- 值长度：1-200字符
- 编码格式：大写字母和数字，最大50字符
- 排序值：0-9999之间的数字

## 扩展开发

### 添加新的字典类型

1. 在 `data-generation-config.js` 中添加字典类型配置
2. 在 `DICTIONARY_DATA_TEMPLATES` 中添加数据模板
3. 更新帮助文档

### 添加新的数据生成功能

1. 在 `generateOtherData` 函数中添加新的逻辑
2. 在 `API_ENDPOINTS` 中配置相关接口
3. 添加相应的命令行参数

## 注意事项

⚠️ **重要提醒**：

1. 请确保目标服务器可访问
2. 请确保Authorization token有效且有足够权限
3. 脚本会自动生成唯一的字典编码，避免重复
4. 请求间会有适当延迟以避免服务器压力
5. 建议先在测试环境验证后再在生产环境运行
6. 定期检查和更新Authorization token
7. 监控日志文件大小，定期清理旧日志

## 故障排除

### 常见问题

1. **认证失败**: 检查Authorization token是否有效
2. **网络连接失败**: 检查服务器地址和网络连接
3. **权限不足**: 确认token有相应的操作权限
4. **数据重复**: 检查是否已存在相同编码的数据

### 调试模式

设置环境变量启用调试模式：

```bash
NODE_ENV=development node scripts/generate-production-data.js
```

## 更新日志

- **v1.0.0**: 初始版本，支持字典值生成
- 后续版本将添加更多数据类型和功能
