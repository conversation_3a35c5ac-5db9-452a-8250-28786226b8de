# 数据生成脚本使用示例

## 基本使用流程

### 1. 查看帮助信息

```bash
pnpm run gen:data:help
```

### 2. 生成疾病类型字典数据

这是你原始需求中的数据，包括：
- 药物成瘾
- 心理健康
- 焦虑障碍
- 睡眠障碍
- 青少年心理健康状况调查数据

```bash
# 使用预定义的npm脚本
pnpm run gen:data:diseases

# 或者直接使用node命令
node scripts/generate-production-data.js --type=diseases
```

### 3. 生成所有类型的字典数据

```bash
# 生成疾病、治疗方式、科室等所有类型的字典数据
pnpm run gen:data:all
```

## 实际执行示例

### 示例1：生成疾病字典数据

```bash
$ pnpm run gen:data:diseases

[2025-01-04 14:30:22] [INFO] 生产环境数据生成脚本启动
[2025-01-04 14:30:22] [INFO] 目标服务器: http://*************:31010
[2025-01-04 14:30:22] [INFO] 环境: production
[2025-01-04 14:30:22] [INFO] 开始时间: 2025-01-04 14:30:22
[2025-01-04 14:30:22] [INFO] ==================================================
[2025-01-04 14:30:22] [INFO] 开始生成字典值数据 (类型: diseases)
[2025-01-04 14:30:22] [INFO] 正在创建字典值: 药物成瘾
[2025-01-04 14:30:23] [INFO] 请求成功: /resources/DictionaryValue/newOrUpdateEntity
[2025-01-04 14:30:23] [INFO] 字典值创建成功: 药物成瘾
[2025-01-04 14:30:24] [INFO] 正在创建字典值: 心理健康
[2025-01-04 14:30:25] [INFO] 请求成功: /resources/DictionaryValue/newOrUpdateEntity
[2025-01-04 14:30:25] [INFO] 字典值创建成功: 心理健康
...
[2025-01-04 14:30:45] [INFO] 执行结果汇总:
[2025-01-04 14:30:45] [INFO] ==================================================

[2025-01-04 14:30:45] [INFO] 📚 diseases 字典值生成结果:
[2025-01-04 14:30:45] [INFO] 1. ✅ 药物成瘾 (DISEASE20250104143022)
[2025-01-04 14:30:45] [INFO] 2. ✅ 心理健康 (DISEASE20250104143023)
[2025-01-04 14:30:45] [INFO] 3. ✅ 焦虑障碍 (DISEASE20250104143024)
[2025-01-04 14:30:45] [INFO] 4. ✅ 睡眠障碍 (DISEASE20250104143025)
[2025-01-04 14:30:45] [INFO] 5. ✅ 青少年心理健康状况调查数据 (DISEASE20250104143026)
[2025-01-04 14:30:45] [INFO] diseases 成功率: 5/5 (100.0%)
[2025-01-04 14:30:45] [INFO] 📈 总体成功率: 5/5 (100.0%)
[2025-01-04 14:30:45] [INFO] ⏰ 结束时间: 2025-01-04 14:30:45
```

### 示例2：生成所有字典类型

```bash
$ pnpm run gen:data:all

[2025-01-04 14:35:00] [INFO] 生产环境数据生成脚本启动
[2025-01-04 14:35:00] [INFO] 开始生成所有类型的字典数据
[2025-01-04 14:35:00] [INFO] 正在生成 diseases 类型的字典数据
...
[2025-01-04 14:35:30] [INFO] 正在生成 treatments 类型的字典数据
...
[2025-01-04 14:35:50] [INFO] 正在生成 departments 类型的字典数据
...
[2025-01-04 14:36:10] [INFO] 执行结果汇总:
[2025-01-04 14:36:10] [INFO] 📚 diseases 字典值生成结果:
[2025-01-04 14:36:10] [INFO] 1. ✅ 药物成瘾 (DISEASE20250104143500)
...
[2025-01-04 14:36:10] [INFO] 📚 treatments 字典值生成结果:
[2025-01-04 14:36:10] [INFO] 1. ✅ 药物治疗 (TREATMENT20250104143530)
...
[2025-01-04 14:36:10] [INFO] 📚 departments 字典值生成结果:
[2025-01-04 14:36:10] [INFO] 1. ✅ 精神科 (DEPT20250104143550)
...
[2025-01-04 14:36:10] [INFO] 📈 总体成功率: 19/19 (100.0%)
```

## 生成的数据格式

每个字典值的数据格式如下：

```json
{
  "id": 0,
  "dictionaryId": 7,
  "code": "DISEASE20250104143022",
  "title": "药物成瘾",
  "sortOrder": 100,
  "state": "启用",
  "value": "药物成瘾",
  "isDefault": false
}
```

## 对应的curl请求

脚本生成的请求等价于以下curl命令：

```bash
curl 'http://*************:31010/resources/DictionaryValue/newOrUpdateEntity' \
  -H 'Authorization: Bearer 5EWyUg-rmoLoFbhpMrmUln2IOXcIfv7B1dP0dYLx6j5xMwiW9_MzKryDwpAIoQvFGAVzZ74ojE642NwQ5XEiCDwMTu4B6a77MWt1kDI8djITCmZnWPld3L2OaZuiPivd' \
  -H 'Referer: http://*************:31010/background/dictionary' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' \
  --data-raw '{"id":0,"dictionaryId":7,"code":"DISEASE20250104143022","title":"药物成瘾","sortOrder":100,"state":"启用","value":"药物成瘾","isDefault":false}' \
  --insecure
```

## 错误处理示例

如果遇到错误，脚本会显示详细的错误信息：

```bash
[2025-01-04 14:30:25] [ERROR] 请求失败: /resources/DictionaryValue/newOrUpdateEntity
HTTP状态码: 401 - 认证失败，请检查Authorization token
详细信息: Unauthorized access
[2025-01-04 14:30:26] [WARN] 请求失败，1000ms后重试: ...
[2025-01-04 14:30:27] [ERROR] 字典值创建失败: 心理健康 - Request failed with status code 401
```

## 日志文件

执行过程中的所有日志都会保存到文件：

```
logs/data-generation-2025-01-04.log
```

## 扩展其他接口

要添加其他接口的数据生成，可以在 `generateOtherData` 函数中添加逻辑：

```javascript
// 在 scripts/generate-production-data.js 中
async function generateOtherData() {
  writeLog('开始生成其他业务数据', 'info');

  // 示例：生成用户数据
  const userData = {
    username: 'test_user',
    email: '<EMAIL>',
    role: 'admin'
  };

  try {
    const result = await sendRequest(API_ENDPOINTS.user.create, userData);
    writeLog('用户数据创建成功', 'info');
    return [{ type: 'user', success: true, result }];
  } catch (error) {
    writeLog(`用户数据创建失败: ${error.message}`, 'error');
    return [{ type: 'user', success: false, error: error.message }];
  }
}
```

## 注意事项

1. **Token有效性**: 确保Authorization token有效且有足够权限
2. **服务器连接**: 确保能够访问目标服务器
3. **数据重复**: 脚本会生成唯一编码，但建议在生产环境运行前先在测试环境验证
4. **请求频率**: 脚本内置了请求间隔，避免对服务器造成压力
5. **日志监控**: 定期检查日志文件，监控执行状态

## 下一步扩展

根据你的需求，可以继续扩展以下功能：

1. **患者数据生成**: 添加患者信息的批量创建
2. **诊断记录生成**: 生成诊断相关的数据
3. **治疗计划生成**: 创建治疗计划数据
4. **统计数据生成**: 生成报表和统计相关的数据
5. **权限数据生成**: 创建用户权限和角色数据

每种数据类型都可以按照类似的模式添加到脚本中。
