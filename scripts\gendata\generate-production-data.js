import axios from 'axios';
import dayjs from 'dayjs';
import fs from 'fs';
import path from 'path';
import {
  ENVIRONMENTS,
  DICTIONARY_TYPES,
  DICTIONARY_DATA_TEMPLATES,
  API_ENDPOINTS,
  GENERATION_RULES,
  DEFAULT_HEADERS,
  VALIDATION_RULES,
  ERROR_HANDLING,
} from './data-generation-config.js';

// 获取环境配置
const ENV = process.env.NODE_ENV || 'production';
const envConfig = ENVIRONMENTS[ENV] || ENVIRONMENTS.production;

// 配置信息
const CONFIG = {
  ...envConfig,
  headers: {
    ...DEFAULT_HEADERS,
    Authorization: envConfig.authorization,
    Referer: envConfig.referer,
  },
};

// 工具函数：生成字典编码
function generateDictionaryCode(prefix = 'DISEASE', includeRandom = false) {
  const rules = GENERATION_RULES.codeGeneration;
  const date = dayjs().format(rules.dateFormat);
  const time = dayjs().format(rules.timeFormat);

  let code = `${prefix}${rules.separator}${date}${time}`;

  if (includeRandom || rules.includeRandom) {
    const randomStr = Math.random()
      .toString(36)
      .substring(2, 2 + rules.randomLength)
      .toUpperCase();
    code += `${rules.separator}${randomStr}`;
  }

  return code;
}

// 工具函数：数据验证
function validateDictionaryData(data) {
  const rules = VALIDATION_RULES.dictionary;
  const errors = [];

  // 验证标题
  if (!data.title || data.title.length < rules.title.minLength || data.title.length > rules.title.maxLength) {
    errors.push(`标题长度必须在${rules.title.minLength}-${rules.title.maxLength}之间`);
  }

  // 验证值
  if (!data.value || data.value.length < rules.value.minLength || data.value.length > rules.value.maxLength) {
    errors.push(`值长度必须在${rules.value.minLength}-${rules.value.maxLength}之间`);
  }

  // 验证编码
  if (!data.code || !rules.code.pattern.test(data.code) || data.code.length > rules.code.maxLength) {
    errors.push(`编码格式不正确或长度超过${rules.code.maxLength}`);
  }

  // 验证排序
  if (
    typeof data.sortOrder !== 'number' ||
    data.sortOrder < rules.sortOrder.min ||
    data.sortOrder > rules.sortOrder.max
  ) {
    errors.push(`排序值必须在${rules.sortOrder.min}-${rules.sortOrder.max}之间`);
  }

  return errors;
}

// 工具函数：创建日志目录
function ensureLogDirectory() {
  const logDir = GENERATION_RULES.logging.logDir;
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  return logDir;
}

// 工具函数：写入日志
function writeLog(message, level = 'info') {
  const timestamp = dayjs().format('YYYY-MM-DD HH:mm:ss');
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

  console.log(logMessage);

  if (GENERATION_RULES.logging.saveToFile) {
    const logDir = ensureLogDirectory();
    const logFile = path.join(logDir, `data-generation-${dayjs().format('YYYY-MM-DD')}.log`);
    fs.appendFileSync(logFile, logMessage + '\n');
  }
}

// 工具函数：延迟执行
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// 工具函数：处理HTTP错误
function handleHttpError(error, url) {
  const status = error.response?.status;
  const statusMessage = ERROR_HANDLING.httpStatusHandling[status] || '未知错误';
  const businessError = error.response?.data?.code;
  const businessMessage = ERROR_HANDLING.businessErrorHandling[businessError] || '';

  let errorMessage = `请求失败: ${url}\n`;
  errorMessage += `HTTP状态码: ${status} - ${statusMessage}\n`;

  if (businessError) {
    errorMessage += `业务错误码: ${businessError} - ${businessMessage}\n`;
  }

  errorMessage += `详细信息: ${error.response?.data?.message || error.message}`;

  return errorMessage;
}

// 工具函数：发送HTTP请求（带重试机制）
async function sendRequest(url, data, method = 'POST') {
  const maxRetries = GENERATION_RULES.request.retryCount;
  const retryDelay = GENERATION_RULES.request.retryDelay;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      writeLog(`发送请求 (尝试 ${attempt}/${maxRetries}): ${method} ${url}`, 'debug');

      const response = await axios({
        method,
        url: `${CONFIG.baseUrl}${url}`,
        headers: CONFIG.headers,
        data,
        timeout: GENERATION_RULES.request.timeout,
        httpsAgent: new (await import('https')).Agent({
          rejectUnauthorized: false,
        }),
      });

      writeLog(`请求成功: ${url}`, 'info');
      writeLog(`响应数据: ${JSON.stringify(response.data, null, 2)}`, 'debug');
      return response.data;
    } catch (error) {
      const errorMessage = handleHttpError(error, url);

      if (attempt === maxRetries) {
        writeLog(errorMessage, 'error');
        throw error;
      } else {
        writeLog(`请求失败，${retryDelay}ms后重试: ${errorMessage}`, 'warn');
        await delay(retryDelay);
      }
    }
  }
}

// 生成字典值数据
function prepareDictionaryData(dataType = 'diseases') {
  const templateData = DICTIONARY_DATA_TEMPLATES[dataType] || DICTIONARY_DATA_TEMPLATES.diseases;

  return templateData.map((item) => {
    const dictType = DICTIONARY_TYPES[item.type];
    return {
      title: item.title,
      value: item.value,
      dictionaryId: dictType.dictionaryId,
      sortOrder: item.sortOrder,
      type: item.type,
    };
  });
}

// 生成字典值
async function generateDictionaryValues() {
  console.log('🚀 开始生成字典值数据...\n');

  const results = [];

  for (let i = 0; i < DICTIONARY_VALUES.length; i++) {
    const item = DICTIONARY_VALUES[i];

    // 生成字典编码，每个项目间隔1秒以确保编码唯一性
    if (i > 0) {
      await delay(1000);
    }

    const dictType = DICTIONARY_TYPES[item.type];
    const code = generateDictionaryCode(dictType.prefix);

    const requestData = {
      id: 0,
      dictionaryId: item.dictionaryId,
      code: code,
      title: item.title,
      sortOrder: item.sortOrder,
      state: '启用',
      value: item.value,
      isDefault: false,
    };

    // 数据验证
    const validationErrors = validateDictionaryData(requestData);
    if (validationErrors.length > 0) {
      writeLog(`数据验证失败: ${item.title} - ${validationErrors.join(', ')}`, 'error');
      results.push({
        title: item.title,
        code: code,
        success: false,
        error: `数据验证失败: ${validationErrors.join(', ')}`,
      });
      continue;
    }

    writeLog(`正在创建字典值: ${item.title}`, 'info');
    writeLog(`生成编码: ${code}`, 'debug');

    try {
      const result = await sendRequest(API_ENDPOINTS.dictionary.createOrUpdate, requestData);
      results.push({
        title: item.title,
        code: code,
        success: true,
        result: result,
      });
      writeLog(`字典值创建成功: ${item.title}`, 'info');
    } catch (error) {
      results.push({
        title: item.title,
        code: code,
        success: false,
        error: error.message,
      });
      writeLog(`字典值创建失败: ${item.title} - ${error.message}`, 'error');
    }

    // 请求间隔，避免服务器压力
    await delay(GENERATION_RULES.request.requestInterval);
  }

  return results;
}

// 生成多种类型的字典数据
async function generateAllDictionaryTypes() {
  writeLog('开始生成所有类型的字典数据', 'info');

  const allResults = {};
  const dataTypes = Object.keys(DICTIONARY_DATA_TEMPLATES);

  for (const dataType of dataTypes) {
    writeLog(`正在生成 ${dataType} 类型的字典数据`, 'info');
    try {
      const results = await generateDictionaryValues(dataType);
      allResults[dataType] = results;

      // 类型间延迟
      await delay(2000);
    } catch (error) {
      writeLog(`生成 ${dataType} 类型数据失败: ${error.message}`, 'error');
      allResults[dataType] = [];
    }
  }

  return allResults;
}

// 其他数据生成函数的占位符
async function generateOtherData() {
  writeLog('其他数据生成功能待实现', 'info');
  // TODO: 在这里添加其他接口的数据生成逻辑
  // 例如：用户数据、患者数据、诊断记录等
  return [];
}

// 主函数
async function main(options = {}) {
  writeLog('生产环境数据生成脚本启动', 'info');
  writeLog(`目标服务器: ${CONFIG.baseUrl}`, 'info');
  writeLog(`环境: ${ENV}`, 'info');
  writeLog(`开始时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, 'info');
  writeLog('='.repeat(50), 'info');

  try {
    let dictionaryResults = {};

    // 根据选项决定生成哪些数据
    if (options.dictionaryType) {
      // 生成指定类型的字典数据
      const results = await generateDictionaryValues(options.dictionaryType);
      dictionaryResults[options.dictionaryType] = results;
    } else if (options.allDictionaries) {
      // 生成所有类型的字典数据
      dictionaryResults = await generateAllDictionaryTypes();
    } else {
      // 默认只生成疾病类型
      const results = await generateDictionaryValues('diseases');
      dictionaryResults.diseases = results;
    }

    // 汇总结果
    writeLog('执行结果汇总:', 'info');
    writeLog('='.repeat(50), 'info');

    let totalSuccess = 0;
    let totalCount = 0;

    Object.entries(dictionaryResults).forEach(([type, results]) => {
      writeLog(`\n📚 ${type} 字典值生成结果:`, 'info');
      results.forEach((item, index) => {
        const status = item.success ? '✅' : '❌';
        writeLog(`${index + 1}. ${status} ${item.title} (${item.code})`, 'info');
      });

      const successCount = results.filter((item) => item.success).length;
      totalSuccess += successCount;
      totalCount += results.length;

      writeLog(
        `${type} 成功率: ${successCount}/${results.length} (${((successCount / results.length) * 100).toFixed(1)}%)`,
        'info'
      );
    });

    writeLog(
      `\n📈 总体成功率: ${totalSuccess}/${totalCount} (${((totalSuccess / totalCount) * 100).toFixed(1)}%)`,
      'info'
    );
    writeLog(`⏰ 结束时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, 'info');

    return dictionaryResults;
  } catch (error) {
    writeLog(`脚本执行失败: ${error.message}`, 'error');
    process.exit(1);
  }
}

// 命令行参数处理
const args = process.argv.slice(2);

// 解析命令行参数
function parseArguments() {
  const options = {
    help: args.includes('--help') || args.includes('-h'),
    dictionaryOnly: args.includes('--dictionary-only'),
    allDictionaries: args.includes('--all-dictionaries'),
    otherOnly: args.includes('--other-only'),
    dictionaryType: null,
    environment: null,
  };

  // 解析字典类型参数
  const typeIndex = args.findIndex((arg) => arg.startsWith('--type='));
  if (typeIndex !== -1) {
    options.dictionaryType = args[typeIndex].split('=')[1];
  }

  // 解析环境参数
  const envIndex = args.findIndex((arg) => arg.startsWith('--env='));
  if (envIndex !== -1) {
    options.environment = args[envIndex].split('=')[1];
    process.env.NODE_ENV = options.environment;
  }

  return options;
}

const options = parseArguments();

if (options.help) {
  console.log(`
📖 生产环境数据生成脚本使用说明

🚀 基本用法:
  pnpm run gen:data                           # 生成默认疾病字典数据
  node scripts/generate-production-data.js

🔧 可用选项:
  --help, -h                                  # 显示帮助信息
  --dictionary-only                           # 仅生成字典值数据（默认疾病类型）
  --all-dictionaries                          # 生成所有类型的字典数据
  --type=<类型>                               # 生成指定类型的字典数据
  --env=<环境>                                # 指定运行环境 (production/staging/development)
  --other-only                                # 仅生成其他数据（待实现）

📚 可用的字典类型:
  diseases                                    # 疾病类型
  treatments                                  # 治疗方式
  departments                                 # 科室

📝 示例:
  pnpm run gen:data --dictionary-only         # 生成疾病字典
  pnpm run gen:data --all-dictionaries        # 生成所有字典类型
  pnpm run gen:data --type=treatments         # 生成治疗方式字典
  pnpm run gen:data --env=staging             # 在staging环境运行

⚠️  注意事项:
  1. 请确保目标服务器可访问
  2. 请确保Authorization token有效
  3. 脚本会自动生成唯一的字典编码
  4. 请求间会有适当延迟以避免服务器压力
  5. 日志文件会保存在 ./logs 目录下
  `);
  process.exit(0);
}

// 根据参数执行不同功能
if (options.dictionaryOnly || options.allDictionaries || options.dictionaryType) {
  const mainOptions = {
    dictionaryType: options.dictionaryType,
    allDictionaries: options.allDictionaries,
  };

  main(mainOptions)
    .then(() => {
      writeLog('字典值生成完成', 'info');
    })
    .catch((error) => {
      writeLog(`字典值生成失败: ${error.message}`, 'error');
      process.exit(1);
    });
} else if (options.otherOnly) {
  generateOtherData()
    .then(() => {
      writeLog('其他数据生成完成', 'info');
    })
    .catch((error) => {
      writeLog(`其他数据生成失败: ${error.message}`, 'error');
      process.exit(1);
    });
} else {
  // 执行默认流程
  main()
    .then(() => {
      writeLog('数据生成完成', 'info');
    })
    .catch((error) => {
      writeLog(`数据生成失败: ${error.message}`, 'error');
      process.exit(1);
    });
}
