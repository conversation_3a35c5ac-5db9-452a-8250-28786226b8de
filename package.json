{"name": "cbd-ui", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "devhost": "vite --host", "build": "vite build && node ./scripts/zip-dist.js && node ./scripts/copy-dist.js", "copy:dist": "node ./scripts/copy-dist.js", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --outDir ./docker/dist/", "preview": "vite preview", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint": "eslint . --ext .vue,.ts,.tsx,.cjs,.mjs --fix", "format": "prettier --write src/", "genswagger": "node ./scripts/generateApi.js http://fast9.shenzhuo.vip:32271/resources/v3/api-docs --part --swagger", "genmap": "node ./scripts/api_mapping_script.js", "genapi": "npm run genmap && npm run genswagger", "gen:data": "node ./scripts/gendata/generate-production-data.js", "gen:data:test": "node ./scripts/gendata/generate-production-data.js --test", "gen:data:help": "node ./scripts/gendata/generate-production-data.js --help", "test:config": "node ./scripts/gendata/test-config.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@jsplumb/browser-ui": "^6.2.10", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^10.11.1", "axios": "^1.8.1", "caniuse-lite": "^1.0.30001712", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.7", "lodash-es": "^4.17.21", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^3.2.3", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.24.0", "@tailwindcss/vite": "^4.1.3", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.23", "@vitejs/plugin-vue": "^5.2.1", "archiver": "^7.0.1", "chinese-to-pinyin": "^1.3.1", "cross-env": "^7.0.3", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "rimraf": "^6.0.1", "sass": "^1.86.3", "tailwindcss": "^4.1.3", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1", "unplugin-auto-import": "^0.17.8", "vite": "^6.2.5", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}